<!--
 * @Date         : 2025-03-28 18:28:38
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2025-03-28 17:23:01
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import dayjs from "dayjs";

const props = defineProps<{
  orgId: number;
  workerList: any[];
}>();

const loading = ref(false);
const month = ref(new Date());
const workerId = ref();

const getData = () => {
  console.log(month.value, props.orgId);
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

watch(
  () => props.orgId,
  () => {
    workerId.value = undefined;
    getData();
  }
);
</script>

<template>
  <el-card v-loading="loading" class="h-530px">
    <div class="flex justify-between items-center">
      <span class="font-bold text-16px">月度营收日历</span>
      <el-select-v2
        v-model="workerId"
        :options="workerList"
        placeholder="请选择坐席"
        filterable
        @change="getData"
      />
      <el-date-picker
        v-model="month"
        type="month"
        style="width: 180px"
        :clearable="false"
        @change="getData"
      />
    </div>
    <el-calendar v-model="month">
      <template #dateCell="{ date, data }">
        <div class="flex flex-col gap-6px justify-center items-center">
          <span>{{ dayjs(data.day).date() }}</span>
          <span class="c-#409EFF">¥1234565</span>
        </div>
      </template>
    </el-calendar>
  </el-card>
</template>

<style scoped lang="scss">
:deep(.el-calendar-day) {
  height: 55px;
}
</style>
