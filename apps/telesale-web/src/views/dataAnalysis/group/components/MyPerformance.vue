<!--
 * @Date         : 2025-07-17 16:18:29
 * @Description  : 我的业绩
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import dayjs from "dayjs";
import { progressTextMap } from "../data";

const month = ref(dayjs().format("YYYY-MM"));

const progress = ref(70);
const progressText = computed(() => {
  return progressTextMap[progress.value];
});

const getData = () => {};
</script>

<template>
  <el-card>
    <div class="w-100% flex justify-between gap-20px">
      <div class="w-180px">
        <el-button type="primary">我的业绩排名：NO.34</el-button>
      </div>
      <div class="flex-1 flex gap-10px justify-center items-center w-100%">
        <span class="font-bold">本月业绩：3000</span>
        <div class="flex-1 text-center">
          <div class="c-red">{{ progress }}%</div>
          <el-progress
            :text-inside="true"
            :stroke-width="24"
            :percentage="progress"
          />
          <div class="c-red">{{ progressText }}</div>
        </div>
        <span class="font-bold">本月目标：6000</span>
      </div>
      <div class="w-200px">
        <el-date-picker
          v-model="month"
          type="month"
          style="width: 180px"
          value-format="YYYY-MM"
          :clearable="false"
          @change="getData"
        />
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped></style>
